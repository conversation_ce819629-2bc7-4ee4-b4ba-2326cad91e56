export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
}

export interface StructuredData {
  '@context': string;
  '@type': string;
  [key: string]: any;
}

// Base URL - should be updated to actual domain
export const BASE_URL = 'https://ouroborosproductions.be';

// Default SEO values
export const DEFAULT_SEO: SEOData = {
  title: 'Productiehuis Ouroboros | Voor makers met een verhaal',
  description: 'Productiehuis Ouroboros is een gedreven productiehuis dat zich focust op aanstormend talent in de podiumkunsten. Wij geloven in de kracht van verhalen en willen jonge makers kansen geven om kunst te produceren die ontroert en doet reflecteren.',
  keywords: 'productiehuis, podiumkunsten, theater, muziek, performance, jonge makers, aanstormend talent, Ouroboros, België, Leuven, kunst, cultuur, voorstellingen, producties',
  image: `${BASE_URL}/og-image.jpg`,
  url: BASE_URL,
  type: 'website'
};

// Generate structured data for organization
export const generateOrganizationStructuredData = (): StructuredData => ({
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Productiehuis Ouroboros',
  alternateName: 'Ouroboros Productions',
  url: BASE_URL,
  logo: `${BASE_URL}/logo/logo_long_blue_full.svg`,
  description: 'Een gedreven productiehuis dat zich focust op aanstormend talent in de podiumkunsten. Voor kunst die verbindt, ontroert en doet reflecteren.',
  foundingDate: '2020',
  address: {
    '@type': 'PostalAddress',
    addressCountry: 'BE',
    addressRegion: 'Vlaams-Brabant',
    addressLocality: 'Leuven'
  },
  contactPoint: {
    '@type': 'ContactPoint',
    telephone: '+32-499-96-85-96',
    contactType: 'customer service',
    email: '<EMAIL>',
    availableLanguage: ['Dutch', 'English']
  },
  sameAs: [
    'https://www.instagram.com/ouroborosproductions',
    'https://www.facebook.com/ouroborosproductions'
  ],
  areaServed: 'Belgium',
  knowsAbout: [
    'Theater',
    'Podiumkunsten',
    'Performance',
    'Muziek',
    'Cultuur',
    'Jonge makers',
    'Artistieke productie'
  ]
});

// Generate structured data for theater productions
export const generateTheaterProductionStructuredData = (project: any): StructuredData => ({
  '@context': 'https://schema.org',
  '@type': 'TheaterEvent',
  name: project.title,
  description: project.description,
  image: project.imageUrl.startsWith('http') ? project.imageUrl : `${BASE_URL}${project.imageUrl}`,
  url: `${BASE_URL}/projects/${project.id}`,
  organizer: {
    '@type': 'Organization',
    name: 'Productiehuis Ouroboros',
    url: BASE_URL
  },
  performer: project.people?.map((person: any) => ({
    '@type': 'Person',
    name: person.name,
    jobTitle: person.role
  })) || [],
  genre: 'Theater',
  inLanguage: 'nl',
  audience: {
    '@type': 'Audience',
    audienceType: 'General Public'
  }
});

// Generate structured data for creative works
export const generateCreativeWorkStructuredData = (project: any): StructuredData => ({
  '@context': 'https://schema.org',
  '@type': 'CreativeWork',
  name: project.title,
  description: project.description,
  image: project.imageUrl.startsWith('http') ? project.imageUrl : `${BASE_URL}${project.imageUrl}`,
  url: `${BASE_URL}/projects/${project.id}`,
  creator: {
    '@type': 'Organization',
    name: 'Productiehuis Ouroboros',
    url: BASE_URL
  },
  contributor: project.people?.map((person: any) => ({
    '@type': 'Person',
    name: person.name,
    jobTitle: person.role
  })) || [],
  genre: 'Performing Arts',
  inLanguage: 'nl',
  keywords: `${project.title}, theater, podiumkunsten, ${project.people?.map((p: any) => p.name).join(', ') || ''}`,
  dateCreated: new Date().toISOString(),
  publisher: {
    '@type': 'Organization',
    name: 'Productiehuis Ouroboros',
    url: BASE_URL
  }
});

// Generate breadcrumb structured data
export const generateBreadcrumbStructuredData = (items: Array<{name: string, url: string}>): StructuredData => ({
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  itemListElement: items.map((item, index) => ({
    '@type': 'ListItem',
    position: index + 1,
    name: item.name,
    item: item.url
  }))
});

// Generate FAQ structured data
export const generateFAQStructuredData = (): StructuredData => ({
  '@context': 'https://schema.org',
  '@type': 'FAQPage',
  mainEntity: [
    {
      '@type': 'Question',
      name: 'Wat is Productiehuis Ouroboros?',
      acceptedAnswer: {
        '@type': 'Answer',
        text: 'Productiehuis Ouroboros is een gedreven productiehuis dat zich focust op aanstormend talent in de podiumkunsten. We geloven in de kracht van verhalen en willen jonge makers kansen geven om kunst te produceren die ontroert en doet reflecteren.'
      }
    },
    {
      '@type': 'Question',
      name: 'Welke soorten producties maken jullie?',
      acceptedAnswer: {
        '@type': 'Answer',
        text: 'We produceren diverse vormen van podiumkunst, waaronder theater, muziek, performance en multidisciplinaire voorstellingen. Onze focus ligt op innovatieve en betekenisvolle kunst die een breed publiek kan raken.'
      }
    },
    {
      '@type': 'Question',
      name: 'Hoe kan ik samenwerken met Productiehuis Ouroboros?',
      acceptedAnswer: {
        '@type': 'Answer',
        text: 'Ben je een jonge maker met een verhaal? Neem contact met ons op via ons contactformulier of bel ons op +32 499 96 85 96. We kijken graag naar mogelijkheden voor samenwerking.'
      }
    }
  ]
});

// Utility function to generate meta tags
export const generateMetaTags = (seoData: SEOData) => {
  const tags = [
    { name: 'description', content: seoData.description },
    { name: 'keywords', content: seoData.keywords || DEFAULT_SEO.keywords },
    { property: 'og:title', content: seoData.title },
    { property: 'og:description', content: seoData.description },
    { property: 'og:type', content: seoData.type || 'website' },
    { property: 'og:url', content: seoData.url || BASE_URL },
    { property: 'og:image', content: seoData.image || DEFAULT_SEO.image },
    { property: 'og:site_name', content: 'Productiehuis Ouroboros' },
    { property: 'og:locale', content: 'nl_BE' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: seoData.title },
    { name: 'twitter:description', content: seoData.description },
    { name: 'twitter:image', content: seoData.image || DEFAULT_SEO.image },
    { name: 'twitter:site', content: '@OuroborosHuis' }
  ];

  if (seoData.publishedTime) {
    tags.push({ property: 'article:published_time', content: seoData.publishedTime });
  }

  if (seoData.modifiedTime) {
    tags.push({ property: 'article:modified_time', content: seoData.modifiedTime });
  }

  if (seoData.author) {
    tags.push({ property: 'article:author', content: seoData.author });
  }

  if (seoData.section) {
    tags.push({ property: 'article:section', content: seoData.section });
  }

  return tags;
};

// Utility function to truncate text for descriptions
export const truncateText = (text: string, maxLength: number = 160): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3).trim() + '...';
};

// Generate project-specific SEO data
export const generateProjectSEO = (project: any): SEOData => {
  const description = truncateText(project.description, 155);
  const keywords = `${project.title}, theater, podiumkunsten, Ouroboros, ${project.people?.map((p: any) => p.name).join(', ') || ''}`;
  
  return {
    title: `${project.title} | Productiehuis Ouroboros`,
    description,
    keywords,
    image: project.imageUrl.startsWith('http') ? project.imageUrl : `${BASE_URL}${project.imageUrl}`,
    url: `${BASE_URL}/projects/${project.id}`,
    type: 'article',
    section: 'Projecten',
    author: 'Productiehuis Ouroboros'
  };
};
