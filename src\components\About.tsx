import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowR<PERSON>, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger, DialogClose } from "@/components/ui/dialog";

type TeamMember = {
  id: string;
  name: string;
  role: string;
  image: string;
  bio: string;
  photographer?: string;
};

const About = () => {
  const [hovered, setHovered] = useState<string | null>(null);
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);

  const teamMembers: TeamMember[] = [
  {
    id: "maarten",
    name: "Maarte<PERSON> Min<PERSON>",
    role: "Mede-oprichter",
    image: "/about/maarten.jpeg",
    bio: "Maarten Minne behaalde een master in Handelswetenschappen en volmaakte zijn ople<PERSON> met de manama Culturele Studies aan de KULeuven. Hij begon zijn loop<PERSON> in de internationale ngo-wereld, maar stapte in 2006 over naar de kunstensector. Hij werkte zowel bij grote cultuurhuizen als het Leuvense STUK Kunstencentrum en het provinciale Cultuurhuis de Warande in Turnhout als bij het dansgezelschap Rosas van Anne Teresa De Keersmaeker en het theatergezelschap Het nieuwstedelijk uit Leuven, Hasselt en Genk. Zijn werkwereld is tot op vandaag het faciliteren van artistieke productie en presentatie. Hij begeleide verschillende culturele infrastructuurwerken en hielp zakelijke departementen efficiënter werken. Maarten heeft meer dan 20 jaar ervaring in zakelijke leiding en bestuursfuncties. Hij is gepassioneerd in het omzetten van artistieke creaties en beleid in cijfers en omgekeerd. In 2024 werd hij vennoot in BeMove en biedt sindsdien zakelijke dienstverlening aan aan zowel beginnende makers als gerenommeerde culturele instellingen . ",
    // photographer: "Koen Broos"
  },
  {
    id: "karen",
    name: "Karen Feys",
    role: "Mede-oprichter",
    image: "/about/karen.jpeg",
    bio: "Karen Feys werd geboren en groeide op in België. Ze begon haar carrière als klassiek en hedendaags danser in Letland, Rusland en het Verenigd Koninkrijk. Terwijl ze dans en choreografie combineerde met een opleiding in Theatre Dance & Management in Londen, merkte Karen een gebrek aan zakelijke en saleservaring in de kunstensector. Daarom stapte ze over naar de software-industrie om die ervaring op te doen. Na het opdoen van corporate- en saleservaring wilde ze die kennis integreren in een functie binnen de kunstwereld. Ze ging aan de slag bij IMG Artists, waar ze kunstorganisaties begeleidde, zoals Pilobolus, Miami City Ballet en Colin Dunne, onder anderen. In januari 2010 werd ze medeoprichter en uitvoerend directeur van het dansgezelschap van de internationaal bekende Belgisch-Marokkaanse choreograaf Sidi Larbi Cherkaoui. In november 2011 verhuisde Karen naar Pilobolus in New York om Shadowland en Shadowland 2 te produceren en te distribueren. In 2017 specialiseerde ze zich in alternatieve financiering en werd ze hoofd van Het Laatste Bedrijf, een onderdeel van productiehuis De Mensen. Tegenwoordig produceert en beheert ze uiteenlopende projecten in de podiumkunsten.",
    photographer: "Koen Bauters"
  }];

  return (
    <section id="about" className="section-padding bg-ouroboros-background overflow-hidden">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl mb-6 font-trajan text-ouroboros-accent">Over ons</h2>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-lg leading-relaxed text-center text-ouroboros-accent/80 mb-8 max-w-3xl mx-auto"
          >
            Productiehuis Ouroboros is een gedreven productiehuis dat zich focust op aanstormend talent in de podiumkunsten. We streven ernaar om kunst te produceren die niet alleen ontroert, maar ook doet reflecteren. We geloven dat kunst een krachtig middel is om mensen te verbinden, te inspireren en te transformeren. Wij geloven in de kracht van verhalen en willen jonge makers kansen geven. We geven hen ruimte om te experimenteren en hun werk te delen met een breed en divers publiek.
          </motion.p>
          {/* Team member names */}
          <div className="mt-12 mb-8">
            <p className="text-sm text-ouroboros-accent/60 mb-4 font-medium tracking-wider uppercase">Oprichters</p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 sm:gap-12">
              {teamMembers.map((member, index) => (
                <Dialog key={member.id}>
                  <DialogTrigger asChild>
                    <button className="group text-center">
                      <h3 className="text-xl font-trajan text-ouroboros-accent group-hover:text-ouroboros-accent/80 transition-colors duration-300">
                        {member.name}
                      </h3>
                      <p className="text-sm text-ouroboros-accent/70 mt-1 group-hover:text-ouroboros-accent/60 transition-colors duration-300">
                        {member.role}
                      </p>
                      <div className="w-12 h-0.5 bg-ouroboros-accent/20 group-hover:bg-ouroboros-accent/40 transition-colors duration-300 mx-auto mt-2"></div>
                    </button>
                  </DialogTrigger>

                  <DialogContent className="max-w-5xl p-0 overflow-hidden bg-white border-ouroboros-accent/10 w-[90vw]">
                    <div className="flex flex-col md:flex-row h-full">
                      <div className="md:w-2/5 h-full">
                        <div className="relative h-full min-h-[250px] md:min-h-full">
                          <img
                            src={member.image}
                            alt={member.name}
                            className="absolute inset-0 w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />

                          {/* Photographer credits - bottom right */}
                          {member.photographer && (
                            <div className="absolute bottom-4 right-4 z-10 bg-black/50 backdrop-blur-sm rounded-md px-3 py-2 text-white/80 text-sm">
                              <span className="text-white/60">Foto: </span>
                              <span className="text-white/90">{member.photographer}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="md:w-3/5 p-6 md:p-10">
                        <h2 className="font-trajan text-2xl md:text-3xl mb-2 text-ouroboros-accent">{member.name}</h2>
                        <p className="text-sm md:text-base text-ouroboros-accent/70 mb-8">{member.role}</p>

                        <div className="prose prose-base max-w-none text-ouroboros-accent/80 mb-10">
                          <p className="leading-relaxed">{member.bio}</p>
                        </div>

                        <DialogClose asChild>
                          <Button
                            variant="outline"
                            className="group border-ouroboros-accent text-ouroboros-accent hover:bg-ouroboros-accent hover:text-white transition-colors"
                            onClick={() => {
                              const contactSection = document.getElementById('contact');
                              if (contactSection) {
                                contactSection.scrollIntoView({ behavior: 'smooth' });
                              }
                            }}
                          >
                            <span>Neem contact op met {member.name.split(" ")[0]}</span>
                            <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
                          </Button>
                        </DialogClose>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              ))}
            </div>
          </div>

          <div className="flex justify-center">
            <Button
              variant="outline"
              className="group border-ouroboros-accent text-ouroboros-accent hover:bg-ouroboros-accent hover:text-white transition-colors"
              onClick={() => {
                const contactSection = document.getElementById('contact');
                if (contactSection) {
                  contactSection.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            >
              <span>Neem contact op</span>
              <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
