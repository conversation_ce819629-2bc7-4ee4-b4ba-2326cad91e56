import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowR<PERSON>, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger, DialogClose } from "@/components/ui/dialog";

type TeamMember = {
  id: string;
  name: string;
  role: string;
  image: string;
  bio: string;
  photographer?: string;
};

const About = () => {
  const [hovered, setHovered] = useState<string | null>(null);
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);

  const teamMembers: TeamMember[] = [
  {
    id: "maarten",
    name: "Maarte<PERSON> Min<PERSON>",
    role: "Mede-oprichter",
    image: "/about/maarten.jpeg",
    bio: "Maarten Minne behaalde een master in Handelswetenschappen en volmaakte zijn ople<PERSON> met de manama Culturele Studies aan de KULeuven. Hij begon zijn loop<PERSON> in de internationale ngo-wereld, maar stapte in 2006 over naar de kunstensector. Hij werkte zowel bij grote cultuurhuizen als het Leuvense STUK Kunstencentrum en het provinciale Cultuurhuis de Warande in Turnhout als bij het dansgezelschap Rosas van Anne Teresa De Keersmaeker en het theatergezelschap Het nieuwstedelijk uit Leuven, Hasselt en Genk. Zijn werkwereld is tot op vandaag het faciliteren van artistieke productie en presentatie. Hij begeleide verschillende culturele infrastructuurwerken en hielp zakelijke departementen efficiënter werken. Maarten heeft meer dan 20 jaar ervaring in zakelijke leiding en bestuursfuncties. Hij is gepassioneerd in het omzetten van artistieke creaties en beleid in cijfers en omgekeerd. In 2024 werd hij vennoot in BeMove en biedt sindsdien zakelijke dienstverlening aan aan zowel beginnende makers als gerenommeerde culturele instellingen . ",
    // photographer: "Koen Broos"
  },
  {
    id: "karen",
    name: "Karen Feys",
    role: "Mede-oprichter",
    image: "/about/karen.jpeg",
    bio: "Karen Feys werd geboren en groeide op in België. Ze begon haar carrière als klassiek en hedendaags danser in Letland, Rusland en het Verenigd Koninkrijk. Terwijl ze dans en choreografie combineerde met een opleiding in Theatre Dance & Management in Londen, merkte Karen een gebrek aan zakelijke en saleservaring in de kunstensector. Daarom stapte ze over naar de software-industrie om die ervaring op te doen. Na het opdoen van corporate- en saleservaring wilde ze die kennis integreren in een functie binnen de kunstwereld. Ze ging aan de slag bij IMG Artists, waar ze kunstorganisaties begeleidde, zoals Pilobolus, Miami City Ballet en Colin Dunne, onder anderen. In januari 2010 werd ze medeoprichter en uitvoerend directeur van het dansgezelschap van de internationaal bekende Belgisch-Marokkaanse choreograaf Sidi Larbi Cherkaoui. In november 2011 verhuisde Karen naar Pilobolus in New York om Shadowland en Shadowland 2 te produceren en te distribueren. In 2017 specialiseerde ze zich in alternatieve financiering en werd ze hoofd van Het Laatste Bedrijf, een onderdeel van productiehuis De Mensen. Tegenwoordig produceert en beheert ze uiteenlopende projecten in de podiumkunsten.",
    photographer: "Koen Bauters"
  }];

  return (
    <section id="about" className="section-padding bg-ouroboros-background overflow-hidden">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex flex-col lg:flex-row gap-12 lg:gap-20">
          <div className="lg:w-1/2">
            <h2 className="text-3xl md:text-4xl mb-6 font-trajan text-ouroboros-accent">Over ons</h2>
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="text-lg leading-relaxed text-left text-ouroboros-accent/80 mb-8"
            >
              Productiehuis Ouroboros is een gedreven productiehuis dat zich focust op aanstormend talent in de podiumkunsten. We streven ernaar om kunst te produceren die niet alleen ontroert, maar ook doet reflecteren. We geloven dat kunst een krachtig middel is om mensen te verbinden, te inspireren en te transformeren. Wij geloven in de kracht van verhalen en willen jonge makers kansen geven. We geven hen ruimte om te experimenteren en hun werk te delen met een breed en divers publiek.
            </motion.p>
            <Button
              variant="outline"
              className="group border-ouroboros-accent text-ouroboros-accent hover:bg-ouroboros-accent hover:text-white transition-colors"
              onClick={() => {
                const contactSection = document.getElementById('contact');
                if (contactSection) {
                  contactSection.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            >
              <span>Neem contact op</span>
              <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="lg:w-1/2 grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            {teamMembers.map(member => (
              <Dialog key={member.id}>
                <DialogTrigger asChild>
                  <motion.div
                    whileHover={{ y: -8 }}
                    transition={{ duration: 0.3 }}
                    onMouseEnter={() => setHovered(member.id)}
                    onMouseLeave={() => setHovered(null)}
                    className="group cursor-pointer"
                  >
                    <div className="overflow-hidden mb-6 rounded-lg shadow-md">
                      <div className="aspect-[3/4] relative overflow-hidden">
                        <img
                          src={member.image}
                          alt={member.name}
                          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-ouroboros-accent/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                        <div className="absolute bottom-0 left-0 w-full p-4 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                          <span className="text-white text-sm font-medium">Klik voor meer info</span>
                        </div>
                      </div>
                    </div>

                    <h3 className="text-xl font-trajan mb-1 text-ouroboros-accent">{member.name}</h3>
                    <p className="text-sm text-ouroboros-accent/70">{member.role}</p>
                  </motion.div>
                </DialogTrigger>

                <DialogContent className="max-w-5xl p-0 overflow-hidden bg-white border-ouroboros-accent/10 w-[90vw]">
                  <div className="flex flex-col md:flex-row h-full">
                    <div className="md:w-2/5 h-full">
                      <div className="relative h-full min-h-[250px] md:min-h-full">
                        <img
                          src={member.image}
                          alt={member.name}
                          className="absolute inset-0 w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />

                        {/* Photographer credits - bottom right */}
                        {member.photographer && (
                          <div className="absolute bottom-4 right-4 z-10 bg-black/50 backdrop-blur-sm rounded-md px-3 py-2 text-white/80 text-sm">
                            <span className="text-white/60">Foto: </span>
                            <span className="text-white/90">{member.photographer}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="md:w-3/5 p-6 md:p-10">
                      <h2 className="font-trajan text-2xl md:text-3xl mb-2 text-ouroboros-accent">{member.name}</h2>
                      <p className="text-sm md:text-base text-ouroboros-accent/70 mb-8">{member.role}</p>

                      <div className="prose prose-base max-w-none text-ouroboros-accent/80 mb-10">
                        <p className="leading-relaxed">{member.bio}</p>
                      </div>

                      <DialogClose asChild>
                        <Button
                          variant="outline"
                          className="group border-ouroboros-accent text-ouroboros-accent hover:bg-ouroboros-accent hover:text-white transition-colors"
                          onClick={() => {
                            const contactSection = document.getElementById('contact');
                            if (contactSection) {
                              contactSection.scrollIntoView({ behavior: 'smooth' });
                            }
                          }}
                        >
                          <span>Neem contact op met {member.name.split(" ")[0]}</span>
                          <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
                        </Button>
                      </DialogClose>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default About;
