import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { 
  SEOData, 
  StructuredData, 
  BASE_URL,
  DEFAULT_SEO,
  generateOrganizationStructuredData,
  generateFAQStructuredData,
  generateBreadcrumbStructuredData,
  generateProjectSEO,
  generateTheaterProductionStructuredData,
  generateCreativeWorkStructuredData
} from '../utils/seo';

interface UseSEOReturn {
  seoData: SEOData;
  structuredData: StructuredData[];
  canonical: string;
}

interface UseSEOProps {
  pageType?: 'home' | 'project' | 'about' | 'contact';
  projectData?: any;
  customSEO?: Partial<SEOData>;
}

export const useSEO = ({ 
  pageType = 'home', 
  projectData, 
  customSEO = {} 
}: UseSEOProps = {}): UseSEOReturn => {
  const location = useLocation();

  const seoData = useMemo(() => {
    let baseSEO: SEOData;

    switch (pageType) {
      case 'project':
        if (projectData) {
          baseSEO = generateProjectSEO(projectData);
        } else {
          baseSEO = {
            ...DEFAULT_SEO,
            title: 'Projecten | Productiehuis Ouroboros',
            description: 'Ontdek onze diverse projecten in de podiumkunsten. Van theater tot muziek, van performance tot multidisciplinaire voorstellingen.',
            url: `${BASE_URL}/projects`
          };
        }
        break;

      case 'about':
        baseSEO = {
          ...DEFAULT_SEO,
          title: 'Over ons | Productiehuis Ouroboros',
          description: 'Leer meer over Productiehuis Ouroboros en ons team. Een gedreven productiehuis dat zich focust op aanstormend talent in de podiumkunsten.',
          url: `${BASE_URL}/#about`,
          keywords: 'over ons, team, productiehuis, Ouroboros, podiumkunsten, makers, talent'
        };
        break;

      case 'contact':
        baseSEO = {
          ...DEFAULT_SEO,
          title: 'Contact | Productiehuis Ouroboros',
          description: 'Neem contact op met Productiehuis Ouroboros. Heb je een idee of creatie in gedachten? Wij helpen je graag om jouw verhaal tot leven te brengen.',
          url: `${BASE_URL}/#contact`,
          keywords: 'contact, samenwerking, productiehuis, Ouroboros, makers, projecten'
        };
        break;

      default: // home
        baseSEO = DEFAULT_SEO;
        break;
    }

    return {
      ...baseSEO,
      ...customSEO
    };
  }, [pageType, projectData, customSEO]);

  const structuredData = useMemo(() => {
    const data: StructuredData[] = [];

    // Always include organization data
    data.push(generateOrganizationStructuredData());

    // Add page-specific structured data
    switch (pageType) {
      case 'home':
        // Add FAQ for homepage
        data.push(generateFAQStructuredData());
        
        // Add breadcrumb for homepage
        data.push(generateBreadcrumbStructuredData([
          { name: 'Home', url: BASE_URL }
        ]));
        break;

      case 'project':
        if (projectData) {
          // Add theater production and creative work data
          data.push(generateTheaterProductionStructuredData(projectData));
          data.push(generateCreativeWorkStructuredData(projectData));
          
          // Add breadcrumb for project
          data.push(generateBreadcrumbStructuredData([
            { name: 'Home', url: BASE_URL },
            { name: 'Projecten', url: `${BASE_URL}/#projects` },
            { name: projectData.title, url: `${BASE_URL}/projects/${projectData.id}` }
          ]));
        }
        break;

      case 'about':
        // Add breadcrumb for about
        data.push(generateBreadcrumbStructuredData([
          { name: 'Home', url: BASE_URL },
          { name: 'Over ons', url: `${BASE_URL}/#about` }
        ]));
        break;

      case 'contact':
        // Add breadcrumb for contact
        data.push(generateBreadcrumbStructuredData([
          { name: 'Home', url: BASE_URL },
          { name: 'Contact', url: `${BASE_URL}/#contact` }
        ]));
        break;
    }

    return data;
  }, [pageType, projectData]);

  const canonical = useMemo(() => {
    if (customSEO?.url) {
      return customSEO.url;
    }
    
    const currentPath = location.pathname;
    const currentHash = location.hash;
    
    if (currentPath === '/' && currentHash) {
      return `${BASE_URL}/${currentHash}`;
    }
    
    return `${BASE_URL}${currentPath}`;
  }, [location, customSEO]);

  return {
    seoData,
    structuredData,
    canonical
  };
};
